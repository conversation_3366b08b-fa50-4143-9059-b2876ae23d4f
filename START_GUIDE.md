# 慧通材质库应用启动指南

## 项目概述

慧通材质库是一个用于管理3D模型和材质的应用。

**技术栈:**
- 前端：React + Vite + Three.js
- 后端：Node.js + Express
- 数据库：PostgreSQL
- ORM：Prisma

## 准备工作

在启动应用前，请确保：

1. 已安装 Node.js (v18+) 和 npm
2. 已安装并启动 PostgreSQL 数据库
3. 已完成项目依赖安装 (`npm install`)

## 本地开发环境设置

### 1. 环境配置

确保 `.env.development` 文件已正确配置：
- 数据库连接信息正确
- 端口设置无冲突 (默认前端 5173，后端 3004)

### 2. 数据库准备

确保 PostgreSQL 数据库服务已启动，并且已创建数据库：

```bash
# 创建数据库(如果未创建)
createdb huitong_material

# 或使用 PostgreSQL 命令
psql -U <用户名>
CREATE DATABASE huitong_material;
```

### 3. 应用启动方法

我们提供了几种启动应用的方式：

#### 方法一：可靠启动（推荐）

此方法会自动检查环境、数据库连接，并确保应用正确启动：

```bash
npm run dev:reliable
```

#### 方法二：标准启动

同时启动前端和后端：

```bash
npm run dev
```

#### 方法三：分开启动

分别启动前端和后端，方便单独调试：

```bash
# 启动前端
npm run dev:frontend

# 启动后端
npm run dev:backend
```

## 常见问题排查

### 数据库连接问题

1. 检查 PostgreSQL 服务是否运行中
2. 确认连接信息是否正确（用户名、密码、主机、端口）
3. 确认数据库是否存在

解决方法：
```bash
# 检查 PostgreSQL 状态
pg_isready

# 检查数据库连接
npx prisma db pull
```

### 端口冲突

前端（默认5173）或后端（默认3004）端口可能被占用。

解决方法：
- 修改 `.env.development` 中的 PORT 值
- 或者使用以下命令释放被占用的端口：
  ```bash
  # 查找占用端口的进程
  lsof -i :<端口号>
  
  # 终止该进程
  kill -9 <进程ID>
  ```

### 环境变量问题

如果应用无法读取环境变量，可能是由于环境文件未被正确加载。

解决方法：
```bash
# 检查环境变量是否正确加载
npm run check:env:simple
```

## 文件上传功能

应用支持上传3D模型文件和材质，文件保存在 `backend/uploads` 目录中。确保该目录存在并有正确的写入权限。

## 数据库管理

使用 Prisma Studio 可视化管理数据库：

```bash
npm run db:studio
```

这将打开一个浏览器窗口，可以直接查看和编辑数据库中的内容。

## 部署注意事项

应用支持通过 Docker 进行部署，详细信息请参考 `DEPLOYMENT.md` 文件。
