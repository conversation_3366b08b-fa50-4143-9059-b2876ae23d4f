import React, { memo, useRef, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment, useTexture } from '@react-three/drei';
import * as THREE from 'three';
import type { MaterialData } from '../../services/api';
import './material-thumbnail.css';

interface MaterialThumbnailProps {
  material: MaterialData;
  active: boolean;
  onClick?: () => void;
  size?: 'default' | 'large' | 'preset';
  textureUrl?: string;
}

// A 1x1 transparent pixel. This is a valid image source used as a placeholder
// to ensure useTexture is always called with a valid URL, complying with Rules of Hooks.
const PLACEHOLDER_IMG = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

// This inner component is necessary to use R3F hooks like useTexture.
const Scene = ({ material, textureUrl }: Pick<MaterialThumbnailProps, 'material' | 'textureUrl'>) => {
  // useTexture must be called unconditionally. We provide a placeholder if textureUrl is absent.
  const map = useTexture(textureUrl || PLACEHOLDER_IMG);

  // Don't render if material data is not yet available to prevent flash of default content.
  if (!material) return null;

  // Safely destructure with default values to prevent crashes if data is missing.
  const { color = '#ffffff', metalness = 0, roughness = 50, glass = 0 } = material.data || {};
  const displayColor = (color === '#000000' && !textureUrl) ? '#ffffff' : color;

  // 优化材质参数转换
  const materialProps = {
    color: displayColor,
    metalness: Math.max(0, Math.min(1, metalness / 100)),
    roughness: Math.max(0, Math.min(1, roughness / 100)),
    transparent: glass > 0,
    opacity: glass > 0 ? Math.max(0, Math.min(1, (100 - glass) / 100)) : 1,
    map: textureUrl ? map : null
  };

  return (
    <>
      <ambientLight intensity={0.7} />
      <directionalLight position={[5, 5, 5]} intensity={1} />
      <mesh>
        <sphereGeometry args={[1, 32, 32]} />
        <meshStandardMaterial {...materialProps} />
      </mesh>
      <Environment preset="city" />
    </>
  );
};

/**
 * Renders a 3D sphere thumbnail for a material.
 * - Uses frameloop="demand" for performance.
 * - Implements a cleanup effect to dispose of the WebGL renderer on unmount, preventing context leaks.
 */
const MaterialThumbnail: React.FC<MaterialThumbnailProps> = ({ material, active, onClick, size = 'default', textureUrl }) => {
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  // Initialize useRef with a value to satisfy TypeScript.
  const invalidateRef = useRef<(() => void) | null>(null);

  // Effect to invalidate the canvas when material properties change, crucial for frameloop="demand".
  useEffect(() => {
    if (invalidateRef.current) {
      invalidateRef.current();
    }
  }, [material, textureUrl]);

  // 清理 WebGLRenderer，防止过多上下文导致 "Context Lost"
  useEffect(() => {
    return () => {
      if (rendererRef.current) {
        try {
          // 正常释放 three.js 资源
          rendererRef.current.dispose();
          // 通过扩展主动丢失上下文，确保浏览器立即回收
          const gl = rendererRef.current.getContext();
          const loseCtxExt = gl?.getExtension('WEBGL_lose_context');
          if (loseCtxExt) loseCtxExt.loseContext();
        } catch (error) {
          console.warn('Error disposing WebGL context:', error);
        } finally {
          // 断开引用
          rendererRef.current = null;
        }
      }
    };
  }, []);

  return (
    <div
      className={`material-item${active ? ' active' : ''} ${size === 'large' ? 'material-item-large' : ''} ${size === 'preset' ? 'material-item-preset' : ''}`}
      onClick={onClick}
    >
      <div className="thumbnail-canvas">
        <Canvas
          frameloop="demand"
          camera={{ position: [0, 0, 1.5], fov: 45 }}
          onCreated={({ gl, invalidate }) => {
            rendererRef.current = gl;
            invalidateRef.current = invalidate;
            invalidate(); // Initial render
          }}
          gl={{ antialias: true, alpha: true, powerPreference: 'high-performance' }}
        >
          <Scene material={material} textureUrl={textureUrl} />
        </Canvas>
      </div>
    </div>
  );
};

// 使用 React.memo，但移除自定义比较函数，以便材质属性变化时能正确重新渲染预览。
export default memo(MaterialThumbnail);
