import React, { useState, useEffect } from 'react';
import { Plus, X } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings | null;
}

export interface MaterialSettings {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
}

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({ 
  onChange, 
  defaultSettings
}) => {
  // 设置默认值
  const defaultColor = defaultSettings?.color || '#B39B9C';
  const defaultMetalness = defaultSettings?.metalness ?? 0.5;
  const defaultRoughness = defaultSettings?.roughness ?? 0.5;
  const defaultOpacity = defaultSettings?.opacity ?? 1;

  // 状态
  const [color, setColor] = useState<string>(defaultColor);
  const [metalness, setMetalness] = useState<number>(defaultMetalness);
  const [roughness, setRoughness] = useState<number>(defaultRoughness);
  const [opacity, setOpacity] = useState<number>(defaultOpacity);
  const [textureUrl, setTextureUrl] = useState<string | undefined>(defaultSettings?.textureUrl);

  // 当defaultSettings改变时，更新内部状态
  useEffect(() => {
    if (defaultSettings) {
      setColor(defaultSettings.color || '#B39B9C');
      setMetalness(defaultSettings.metalness ?? 0.5);
      setRoughness(defaultSettings.roughness ?? 0.5);
      setOpacity(defaultSettings.opacity ?? 1);
      setTextureUrl(defaultSettings.textureUrl);
    }
  }, [defaultSettings]);



  // 更新材质设置
  const updateSettings = (
    newColor?: string, 
    newMetalness?: number, 
    newRoughness?: number, 
    newOpacity?: number, 
    newTextureUrl?: string
  ) => {
    const updatedSettings: MaterialSettings = {
      color: newColor ?? color,
      metalness: newMetalness ?? metalness,
      roughness: newRoughness ?? roughness,
      opacity: newOpacity ?? opacity,
      textureUrl: newTextureUrl ?? textureUrl,
    };

    onChange?.(updatedSettings);
  };

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    updateSettings(newColor);
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const url = URL.createObjectURL(file);
    setTextureUrl(url);
    updateSettings(undefined, undefined, undefined, undefined, url);
  };



  return (
    <div className="custom-material" data-layer="自定义材质">
      <div className="material-property-group column" data-layer="Frame 37">
        <div className="property-label" data-layer="颜色">颜色</div>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />

        </div>
      </div>
      
      <div className="material-property-group" data-layer="Frame 36">
        <div className="property-label" data-layer="金属度">金属度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          value={metalness}
          onChange={(value) => {
            setMetalness(value);
            updateSettings(undefined, value);
          }}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 34">
        <div className="property-label" data-layer="粗糙度">粗糙度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          value={roughness}
          onChange={(value) => {
            setRoughness(value);
            updateSettings(undefined, undefined, value);
          }}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 35">
        <div className="property-label" data-layer="透明度">透明度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          value={opacity}
          onChange={(value) => {
            setOpacity(value);
            updateSettings(undefined, undefined, undefined, value);
          }}
          width="100%"
        />
      </div>
      
      <div className="material-property-group column" data-layer="Frame 43">
        <div className="property-label" data-layer="纹理贴图">纹理贴图</div>
        <label className="texture-upload-area" data-layer="Frame 32">
          {textureUrl ? (
            <div className="texture-preview-wrapper">
              <img src={textureUrl} alt="纹理贴图" className="texture-preview" />
              <div 
                className="remove-texture-icon"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setTextureUrl(undefined);
                  updateSettings(undefined, undefined, undefined, undefined, '');
                }}
              >
                <X size={16} />
              </div>
            </div>
          ) : (
            <>
              <div className="plus-icon" data-layer="plus">
                <Plus size={20} color="var(--color-content-regular)" />
              </div>
              <div className="upload-text" data-layer="上传图片">上传图片</div>
            </>
          )}
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleFileUpload} 
            style={{ display: 'none' }} 
          />
        </label>
      </div>
    </div>
  );
};