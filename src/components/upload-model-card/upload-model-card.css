/* 上传模型卡片样式 - 与模型卡片保持一致的尺寸和外观 */
.upload-model-card {
  display: flex;
  width: 280px;
  height: 280px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-sm);
  gap: var(--spacing-sm);
  border: 1px solid var(--card-border-default);
  background: linear-gradient(144deg, rgba(0, 0, 0, 0.30) 0%, rgba(255, 255, 255, 0.05) 98.73%);
  box-shadow: var(--card-shadow-inset);
  backdrop-filter: blur(var(--spacing-md));
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.upload-model-card:hover {
  border-color: var(--card-border-hover);
  box-shadow: var(--card-shadow-hover);
  transform: translateY(calc(-1 * var(--spacing-xs)));
}

.upload-model-card__icon {
  width:fit-content;
  height:fit-content;
  transition: transform 0.3s ease;
}

.upload-model-card:hover .upload-model-card__icon {
  transform: scale(1.1);
}

.file-input {
  display: none;
}
