.secondary-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  height: var(--button-height);
  background: var(--color-support);
  color: var(--color-content-accent);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.secondary-button:hover:not(:disabled) {
  background: var(--color-bg-hover);
}

.secondary-button:disabled {
  opacity: var(--opacity-muted);
  cursor: not-allowed;
}
.secondary-button__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}
.secondary-button--small {
  padding: var(--spacing-s) var(--spacing-base);
  font-size: var(--font-size-base);
}
.secondary-button--large {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
}
.secondary-button--full-width {
  width: 100%;
  justify-content: center;
}

/* 变体样式 */
.secondary-button--default {
  /* 默认样式已在基础类中定义 */
}

.secondary-button--danger {
  color: var(--color-error);
  border: 1px solid var(--color-error);
  background: transparent;
}

.secondary-button--danger:hover:not(:disabled) {
  background-color: var(--color-error);
  color: var(--color-content-invert);
}