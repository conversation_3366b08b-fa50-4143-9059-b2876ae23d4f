.icon-button {
  align-items: center;
  background-color: transparent;
  border: none;
  border-radius: var(--radius-base);
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  padding: 0;
  position: relative;
  transition: background-color 0.2s ease, opacity 0.2s ease;
}

/* 尺寸变体 */
.icon-button--small {
  height: 24px;
  width: 24px;
}

.icon-button--medium {
  height: 32px;
  width: 32px;
}

.icon-button--large {
  height: 40px;
  width: 40px;
}

/* 图标样式 */
.icon-button__icon {
  height: 16px;
  width: 16px;
  color: var(--color-content-regular);
}

.icon-button--small .icon-button__icon {
  height: 14px;
  width: 14px;
}

.icon-button--large .icon-button__icon {
  height: 20px;
  width: 20px;
}

/* 悬停状态 */
.icon-button:hover:not(:disabled) {
  background-color: var(--color-bg-hover);
}

/* 禁用状态 */
.icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 激活状态 */
.icon-button:active:not(:disabled) {
  background-color: var(--color-bg-hover);
  transform: translateY(1px);
}

/* 危险状态 */
.icon-button--danger:hover:not(:disabled) .icon-button__icon {
  color: var(--color-error);
}