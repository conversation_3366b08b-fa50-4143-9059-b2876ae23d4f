import React, { useState, useRef, useEffect, useCallback } from 'react';
import './slider.css';

interface SliderProps {
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 初始值 */
  defaultValue?: number;
  /** 受控值 */
  value?: number;
  /** 步长 */
  step?: number;
  /** 滑块宽度 */
  width?: string | number;
  /** 禁用状态 */
  disabled?: boolean;
  /** 值变化回调 */
  onChange?: (value: number) => void;
  /** 自定义类名 */
  className?: string;
}

export const Slider: React.FC<SliderProps> = ({
  min = 0,
  max = 100,
  defaultValue = 50,
  value: controlledValue,
  step = 1,
  width = 220,
  disabled = false,
  onChange,
  className = '',
}) => {
  const [internalValue, setInternalValue] = useState<number>(defaultValue);
  
  // 判断是否为受控组件
  const isControlled = controlledValue !== undefined;
  const value = isControlled ? controlledValue : internalValue;
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  // 确保值在范围内
  const clamp = useCallback((val: number): number => {
    return Math.min(Math.max(val, min), max);
  }, [min, max]);

  // 将值四舍五入到最近的步长
  const roundToStep = useCallback((val: number): number => {
    const normalized = val - min;
    const stepsCount = Math.round(normalized / step);
    return min + stepsCount * step;
  }, [min, step]);

  // 计算百分比
  const calculatePercentage = useCallback((val: number): number => {
    return ((clamp(val) - min) * 100) / (max - min);
  }, [clamp, min, max]);

  // 从事件中计算值
  const getValueFromEvent = useCallback(
    (event: React.MouseEvent | MouseEvent | React.TouchEvent | TouchEvent): number => {
      if (!sliderRef.current) return value;

      const slider = sliderRef.current;
      const sliderRect = slider.getBoundingClientRect();

      // 获取事件坐标
      let clientX: number;
      if ('touches' in event) {
        clientX = event.touches[0].clientX;
      } else {
        clientX = event.clientX;
      }

      const offset = clientX - sliderRect.left;
      const percentage = offset / sliderRect.width;
      const rawValue = min + percentage * (max - min);
      return roundToStep(clamp(rawValue));
    },
    [clamp, min, max, roundToStep, value]
  );

  // 更新值的统一方法
  const updateValue = useCallback((newValue: number) => {
    if (!isControlled) {
      setInternalValue(newValue);
    }
    if (onChange) onChange(newValue);
  }, [isControlled, onChange]);

  // 鼠标按下处理
  const handleMouseDown = (event: React.MouseEvent): void => {
    if (disabled) return;
    
    const newValue = getValueFromEvent(event);
    updateValue(newValue);
    setIsDragging(true);
    
    event.preventDefault();
  };

  // 触摸开始处理
  const handleTouchStart = (event: React.TouchEvent): void => {
    if (disabled) return;
    
    const newValue = getValueFromEvent(event);
    updateValue(newValue);
    setIsDragging(true);
  };

  // 鼠标移动和触摸移动处理
  useEffect(() => {
    const handleMove = (event: MouseEvent | TouchEvent): void => {
      if (!isDragging || disabled) return;
      
      const newValue = getValueFromEvent(event);
      updateValue(newValue);
      
      if ('preventDefault' in event) event.preventDefault();
    };

    const handleEnd = (): void => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMove);
      document.addEventListener('mouseup', handleEnd);
      document.addEventListener('touchmove', handleMove);
      document.addEventListener('touchend', handleEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMove);
      document.removeEventListener('mouseup', handleEnd);
      document.removeEventListener('touchmove', handleMove);
      document.removeEventListener('touchend', handleEnd);
    };
  }, [isDragging, getValueFromEvent, updateValue, disabled]);

  // 计算滑块位置百分比
  const percentage = calculatePercentage(value);
  
  // 滑块的样式
  const sliderStyle = {
    width: typeof width === 'number' ? `${width}px` : width
  };
  
  return (
    <div 
      className={`slider ${disabled ? 'slider--disabled' : ''} ${className}`}
      style={sliderStyle}
      ref={sliderRef}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    >
      <div className="slider__track" />
      <div 
        className="slider__fill"
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
};
