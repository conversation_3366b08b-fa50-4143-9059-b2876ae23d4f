/* 欢迎页面样式 - 根据Figma设计稿精准还原 */
.welcome-page {
  min-height: 100vh;
  background: var(--welcome-bg-gradient);
  color: var(--color-content-regular);
  position: relative;
}

.welcome-logo {
  position: fixed;
  top: var(--spacing-xl);
  left: var(--spacing-xl);
  width: 100px;
  height: auto;
  opacity: var(--opacity-disabled);
  cursor: pointer;
  transition: opacity 0.2s ease;
  z-index: 100;
}

.welcome-logo:hover {
  opacity: var(--opacity-secondary);
}

/* 主要内容区域 */
.welcome-main {
  padding: var(--spacing-giant) var(--spacing-xl) var(--spacing-xxxl);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.welcome-content {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 欢迎文字区域 */
.welcome-text {
  text-align: center;
  margin-bottom: var(--spacing-huge);
  color: var(--color-content-accent);
}

.welcome-greeting {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-light);
  margin: 0 0 var(--spacing-sm) 0;
  opacity: var(--opacity-secondary);
  line-height: 1.2;
}

.welcome-title {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-massive) 0;
  line-height: 1.2;
  background: var(--welcome-title-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.welcome-subtitle {
  font-size: var(--font-size-xl);
  margin: 0;
  opacity: var(--opacity-muted);
  line-height: var(--line-height-base);
}

/* 模型区域 */
.welcome-models {
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 模型网格 - 每行固定显示4个280px × 280px的卡片 */
.models-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  animation: fadeInUp 0.6s ease-out;
}

/* 确保所有卡片尺寸统一，并具有灵活性 */
.models-grid > * {
  flex: 1 1 260px; /* flex-grow, flex-shrink, flex-basis */
  max-width: 280px; /* 限制最大宽度，防止过度拉伸 */
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.models-grid > .upload-model-card {
  flex: 1 1 260px; /* flex-grow, flex-shrink, flex-basis */
  max-width: 280px; /* 限制最大宽度，防止过度拉伸 */
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0; /* 移除可能的外边距 */
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式已移至通用Loading组件 */

/* 上传模型卡片固定尺寸 */
.fixed-size-upload-card {
  width: 300px !important;
  height: 300px !important;
  flex-shrink: 0;
}

/* 空状态 */
.welcome-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-massive) var(--spacing-lg);
  text-align: center;
}

.empty-icon {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-xl);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0 0 var(--spacing-base) 0;
}

.empty-description {
  font-size: var(--font-size-lg);
  color: var(--color-content-secondary);
  margin: 0;
  max-width: 400px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .models-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    max-width: 800px;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .models-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--spacing-md);
    max-width: 100%;
    padding: 0 var(--spacing-lg);
  }

  .welcome-main {
    padding: var(--spacing-massive) var(--spacing-md) var(--spacing-xxl);
  }
}

@media (max-width: 480px) {
  .models-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}