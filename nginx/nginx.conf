events {
    worker_connections 1024;
}

http {
    # 包含 MIME 类型定义
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 开启 Gzip 压缩，提升前端加载速度
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    # 定义上游服务 (我们的 Node.js 应用)
    # 'app' 是 docker-compose.yml 中定义的服务名
    # '3001' 是 Dockerfile 中暴露的端口
    upstream backend {
        server app:3001;
    }

    server {
        listen 80;
        server_name localhost; # 您之后需要将其替换为您的域名

        # 根目录，指向挂载的前端静态文件
        root /usr/share/nginx/html;
        index index.html;

        # API 请求的反向代理
        # 为用户上传的文件提供服务
        location /uploads/ {
            alias /var/www/uploads/;
            try_files $uri =404;
            # 可以添加缓存头以优化性能
            expires 1d;
            add_header Cache-Control "public, max-age=86400";
        }

        # 反向代理到 Node.js 后端应用
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 处理所有其他请求，返回 React 应用的入口文件
        # 这对于使用 React Router 的单页应用至关重要
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 为 Let's Encrypt 证书验证准备的 location block
        location ~ /.well-known/acme-challenge/ {
            allow all;
            root /var/www/certbot;
        }
    }
}
