#!/usr/bin/env node

/**
 * 可靠的应用启动脚本
 * 此脚本执行以下操作：
 * 1. 检查必要的环境变量
 * 2. 确保上传目录存在
 * 3. 检查数据库连接
 * 4. 启动应用
 */

import dotenv from 'dotenv';
import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pg from 'pg';
const { Pool } = pg;

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// 强制加载环境变量
const envFile = path.join(rootDir, '.env.development');
console.log(`🔍 加载环境变量从: ${envFile}`);
dotenv.config({ path: envFile, override: true });

// 确保上传目录存在
const uploadsDir = path.join(rootDir, 'backend', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  console.log(`📁 创建上传目录: ${uploadsDir}`);
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 检查必要的环境变量
console.log('🔑 检查环境变量...');
const requiredEnvVars = ['POSTGRES_PRISMA_URL', 'NODE_ENV', 'PORT'];
const missingEnvVars = requiredEnvVars.filter(v => !process.env[v]);

if (missingEnvVars.length > 0) {
  console.error(`❌ 缺少必要的环境变量: ${missingEnvVars.join(', ')}`);
  process.exit(1);
}

// 从 POSTGRES_PRISMA_URL 解析连接信息
const dbUrl = new URL(process.env.POSTGRES_PRISMA_URL);
const dbConfig = {
  user: dbUrl.username || process.env.POSTGRES_USER,
  password: dbUrl.password || process.env.POSTGRES_PASSWORD || '',
  host: dbUrl.hostname || process.env.POSTGRES_HOST,
  port: dbUrl.port || process.env.POSTGRES_PORT,
  database: dbUrl.pathname.substring(1) || process.env.POSTGRES_DB,
  ssl: false
};

console.log(`📊 数据库连接信息:
  - 主机: ${dbConfig.host}
  - 端口: ${dbConfig.port}
  - 用户: ${dbConfig.user}
  - 数据库: ${dbConfig.database}
`);

// 检查数据库连接
async function checkDatabaseConnection() {
  const pool = new Pool(dbConfig);
  try {
    console.log('🔌 尝试连接数据库...');
    const client = await pool.connect();
    console.log('✅ 数据库连接成功!');
    client.release();
    await pool.end();
    return true;
  } catch (err) {
    console.error('❌ 数据库连接失败:', err.message);
    return false;
  }
}

// 检查端口是否可用
function isPortAvailable(port) {
  try {
    const netstat = execSync(`lsof -i:${port}`).toString();
    return netstat.length === 0; // 如果输出为空，则端口可用
  } catch (error) {
    return true; // 如果出错，通常意味着端口没有被占用
  }
}

// 运行 Prisma 命令确保数据库已迁移
function runPrismaCommands() {
  try {
    console.log('🔄 运行 Prisma 生成...');
    execSync('npx prisma generate', { stdio: 'inherit', cwd: rootDir });
    
    console.log('🔄 运行 Prisma 数据库推送...');
    execSync('npx prisma db push', { stdio: 'inherit', cwd: rootDir });
    
    return true;
  } catch (error) {
    console.error('❌ Prisma 命令失败:', error.message);
    return false;
  }
}

// 启动应用
function startApp() {
  // 确保端口未被占用
  const port = process.env.PORT || 3004;
  if (!isPortAvailable(port)) {
    console.error(`❌ 端口 ${port} 已被占用，请修改 PORT 环境变量`);
    process.exit(1);
  }
  
  console.log(`🚀 启动应用...`);
  const frontend = spawn('npm', ['run', 'dev:frontend'], { 
    stdio: 'inherit', 
    shell: true,
    cwd: rootDir
  });
  
  const backend = spawn('npm', ['run', 'dev:backend'], { 
    stdio: 'inherit', 
    shell: true,
    cwd: rootDir
  });
  
  frontend.on('error', (err) => {
    console.error('❌ 前端启动失败:', err);
  });
  
  backend.on('error', (err) => {
    console.error('❌ 后端启动失败:', err);
  });
  
  process.on('SIGINT', () => {
    console.log('\n🛑 接收到终止信号，正在关闭应用...');
    frontend.kill();
    backend.kill();
    process.exit(0);
  });
}

// 主函数
async function main() {
  console.log('🔍 开始环境检查...');
  
  // 检查数据库连接
  const dbConnected = await checkDatabaseConnection();
  if (!dbConnected) {
    console.error('❌ 无法连接到数据库，请检查配置和确保数据库正在运行');
    console.log('💡 提示: 请确保 PostgreSQL 服务正在运行');
    process.exit(1);
  }
  
  // 运行 Prisma 命令
  const prismaSuccess = runPrismaCommands();
  if (!prismaSuccess) {
    console.error('❌ Prisma 命令失败，无法继续');
    process.exit(1);
  }
  
  // 一切检查通过，启动应用
  console.log('✅ 所有检查通过，启动应用...');
  startApp();
}

// 运行主函数
main().catch(err => {
  console.error('❌ 启动过程中发生错误:', err);
  process.exit(1);
});
