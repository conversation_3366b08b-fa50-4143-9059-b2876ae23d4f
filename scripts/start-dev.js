#!/usr/bin/env node

/**
 * 跨平台开发环境启动脚本
 * 自动检测并处理端口冲突，确保应用能够在不同操作系统上稳定运行
 */

import { spawn } from 'child_process';
import { platform } from 'os';
import { createRequire } from 'module';
import dotenv from 'dotenv';

const require = createRequire(import.meta.url);
const { execSync } = require('child_process');

// 配置
const CONFIG = {
  BACKEND_PORT: process.env.PORT || 3001,
  FRONTEND_PORT: 5173,
  MAX_PORT_ATTEMPTS: 10
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查端口是否被占用
function isPortInUse(port) {
  try {
    const isWindows = platform() === 'win32';
    const command = isWindows 
      ? `netstat -ano | findstr :${port}`
      : `lsof -ti:${port}`;
    
    execSync(command, { stdio: 'pipe' });
    return true;
  } catch {
    return false;
  }
}

// 杀死占用端口的进程
async function killPortProcess(port) {
  try {
    const isWindows = platform() === 'win32';

    if (isWindows) {
      // Windows: 获取PID并杀死进程
      const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });
      const lines = result.split('\n').filter(line => line.includes(`:${port}`));

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        const pid = parts[parts.length - 1];
        if (pid && !isNaN(pid)) {
          execSync(`taskkill /F /PID ${pid}`, { stdio: 'pipe' });
          log(`✅ Killed process ${pid} on port ${port}`, 'green');
        }
      }
    } else {
      // Unix-like systems
      const pids = execSync(`lsof -ti:${port}`, { encoding: 'utf8' }).trim();
      if (pids) {
        execSync(`kill -9 ${pids}`, { stdio: 'pipe' });
        log(`✅ Killed process(es) ${pids} on port ${port}`, 'green');
      }
    }

    // 等待端口释放
    let attempts = 0;
    while (isPortInUse(port) && attempts < 10) {
      await new Promise(resolve => setTimeout(resolve, 500));
      attempts++;
    }

    return !isPortInUse(port);
  } catch (error) {
    log(`⚠️  Failed to kill process on port ${port}: ${error.message}`, 'yellow');
    return false;
  }
}

// 检查并清理端口
async function checkAndCleanPorts() {
  log('🔍 Checking ports...', 'cyan');
  
  // 检查后端端口
  if (isPortInUse(CONFIG.BACKEND_PORT)) {
    log(`⚠️  Port ${CONFIG.BACKEND_PORT} is in use`, 'yellow');
    const killed = await killPortProcess(CONFIG.BACKEND_PORT);
    if (!killed) {
      log(`❌ Failed to free port ${CONFIG.BACKEND_PORT}`, 'red');
      process.exit(1);
    }
  }
  
  log('✅ Ports are ready', 'green');
}

// 检查环境变量
function checkEnvironment() {
  log('🔧 Checking environment...', 'cyan');

  // 加载环境变量
  dotenv.config({ path: '.env.local' });
  dotenv.config({ path: '.env' });

  const requiredEnvVars = [
    'POSTGRES_PRISMA_URL',
    'BLOB_READ_WRITE_TOKEN'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missing.length > 0) {
    log(`❌ Missing environment variables: ${missing.join(', ')}`, 'red');
    log('💡 Please check your .env or .env.local file', 'yellow');
    process.exit(1);
  }

  log('✅ Environment variables are configured', 'green');
}

// 检查依赖
function checkDependencies() {
  log('📦 Checking dependencies...', 'cyan');
  
  try {
    execSync('npm list --depth=0', { stdio: 'pipe' });
    log('✅ Dependencies are installed', 'green');
  } catch {
    log('⚠️  Some dependencies might be missing, running npm install...', 'yellow');
    try {
      execSync('npm install', { stdio: 'inherit' });
      log('✅ Dependencies installed successfully', 'green');
    } catch (error) {
      log(`❌ Failed to install dependencies: ${error.message}`, 'red');
      process.exit(1);
    }
  }
}

// 启动开发服务器
function startDevelopment() {
  log('🚀 Starting development servers...', 'cyan');
  
  const isWindows = platform() === 'win32';
  const npmCmd = isWindows ? 'npm.cmd' : 'npm';
  
  const devProcess = spawn(npmCmd, ['run', 'dev'], {
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: 'development'
    }
  });
  
  devProcess.on('error', (error) => {
    log(`❌ Failed to start development servers: ${error.message}`, 'red');
    process.exit(1);
  });
  
  devProcess.on('exit', (code) => {
    if (code !== 0) {
      log(`❌ Development servers exited with code ${code}`, 'red');
      process.exit(code);
    }
  });
  
  // 优雅关闭处理
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down development servers...', 'yellow');
    devProcess.kill('SIGINT');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    log('\n🛑 Shutting down development servers...', 'yellow');
    devProcess.kill('SIGTERM');
    process.exit(0);
  });
}

// 主函数
async function main() {
  try {
    log('🎯 Starting Huitong Material Development Environment', 'bright');
    log(`📍 Platform: ${platform()}`, 'blue');
    log(`📍 Node.js: ${process.version}`, 'blue');
    
    await checkAndCleanPorts();
    checkEnvironment();
    checkDependencies();
    startDevelopment();
    
  } catch (error) {
    log(`❌ Startup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行主函数
main();
