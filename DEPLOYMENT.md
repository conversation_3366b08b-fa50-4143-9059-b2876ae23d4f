# 汇通材质系统 - 部署指南

## 项目迁移完成

本项目已成功从Vercel迁移到完全自托管的本地存储和阿里云部署架构。

## 已完成的优化

### 1. 移除Vercel依赖
- ✅ 删除 `vercel.json` 配置文件
- ✅ 移除 `@vercel/blob` 和 `@vercel/postgres` 依赖
- ✅ 删除Vercel相关的清理脚本
- ✅ 更新项目名称从 `huitong-material-vercel` 到 `huitong-material`

### 2. 本地文件存储系统
- ✅ 配置本地文件上传到 `backend/uploads` 目录
- ✅ 更新文件上传API响应格式
- ✅ 配置nginx静态文件服务

### 3. 数据库配置优化
- ✅ 配置本地PostgreSQL数据库连接
- ✅ 修复数据库连接字符串格式
- ✅ 验证Prisma客户端配置

### 4. 环境配置标准化
- ✅ 创建统一的 `.env` 配置文件
- ✅ 更新开发和生产环境配置
- ✅ 修复端口配置一致性

### 5. Docker和部署配置
- ✅ 完善Dockerfile多阶段构建
- ✅ 优化nginx反向代理配置
- ✅ 配置docker-compose生产环境

## 当前运行状态

### 本地开发环境
- **前端**: http://localhost:5174/
- **后端**: http://localhost:3004/
- **数据库**: PostgreSQL (localhost:5432)
- **文件存储**: 本地 `backend/uploads` 目录

### 环境变量配置
```bash
# 数据库配置
POSTGRES_USER=Bavoch
POSTGRES_DB=huitong_material
POSTGRES_PRISMA_URL=postgresql://Bavoch@localhost:5432/huitong_material?schema=public

# 应用配置
NODE_ENV=development
PORT=3004
UPLOAD_DIR=./backend/uploads
```

## 部署命令

### 本地开发
```bash
# 检查环境配置
npm run check:env

# 启动开发服务器
npm run dev
```

### 生产部署
```bash
# Docker Compose部署
docker-compose up -d

# 手动部署
npm run build
npm run start:prod
```

## 下一步建议

1. **功能测试**: 测试3D模型上传、材质编辑等核心功能
2. **性能优化**: 根据实际使用情况优化文件存储和数据库性能
3. **安全配置**: 为生产环境配置SSL证书和安全策略
4. **监控配置**: 添加应用监控和日志收集

## 技术栈

- **前端**: React + TypeScript + Vite + Three.js
- **后端**: Node.js + Express + Prisma
- **数据库**: PostgreSQL
- **文件存储**: 本地文件系统
- **部署**: Docker + Nginx
- **开发工具**: ESLint + Husky + Concurrently

项目现在完全独立于第三方云服务，可以在任何支持Docker的环境中部署运行。
